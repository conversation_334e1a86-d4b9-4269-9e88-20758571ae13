<?php
// Teste simples para verificar se ZipArchive funciona
echo "Testando ZipArchive...\n";

if (!class_exists('ZipArchive')) {
    echo "ERRO: ZipArchive não está disponível!\n";
    exit;
}

echo "ZipArchive está disponível.\n";

$currentDir = getcwd();
echo "Diretório atual: $currentDir\n";

if (!is_writable($currentDir)) {
    echo "ERRO: Diretório não tem permissão de escrita!\n";
    exit;
}

echo "Diretório tem permissão de escrita.\n";

// Teste de criação de ZIP
$zip = new ZipArchive();
$testZipPath = $currentDir . DIRECTORY_SEPARATOR . 'test_simple.zip';

echo "Tentando criar ZIP em: $testZipPath\n";

$result = $zip->open($testZipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE);

if ($result === TRUE) {
    echo "ZIP criado com sucesso!\n";
    
    // Adicionar um arquivo de teste
    if (file_exists('test_zip.txt')) {
        $zip->addFile('test_zip.txt', 'test_zip.txt');
        echo "Arquivo test_zip.txt adicionado.\n";
    }
    
    $zip->close();
    
    if (file_exists($testZipPath)) {
        echo "Arquivo ZIP foi criado com sucesso: " . filesize($testZipPath) . " bytes\n";
        // Limpar arquivo de teste
        unlink($testZipPath);
        echo "Arquivo de teste removido.\n";
    } else {
        echo "ERRO: Arquivo ZIP não foi criado!\n";
    }
} else {
    echo "ERRO ao criar ZIP: $result\n";
    
    // Mostrar erro específico
    switch ($result) {
        case ZipArchive::ER_OK:
            echo "No error\n";
            break;
        case ZipArchive::ER_MULTIDISK:
            echo "Multi-disk zip archives not supported\n";
            break;
        case ZipArchive::ER_RENAME:
            echo "Renaming temporary file failed\n";
            break;
        case ZipArchive::ER_CLOSE:
            echo "Closing zip archive failed\n";
            break;
        case ZipArchive::ER_SEEK:
            echo "Seek error\n";
            break;
        case ZipArchive::ER_READ:
            echo "Read error\n";
            break;
        case ZipArchive::ER_WRITE:
            echo "Write error\n";
            break;
        case ZipArchive::ER_CRC:
            echo "CRC error\n";
            break;
        case ZipArchive::ER_ZIPCLOSED:
            echo "Containing zip archive was closed\n";
            break;
        case ZipArchive::ER_NOENT:
            echo "No such file\n";
            break;
        case ZipArchive::ER_EXISTS:
            echo "File already exists\n";
            break;
        case ZipArchive::ER_OPEN:
            echo "Can't open file\n";
            break;
        case ZipArchive::ER_TMPOPEN:
            echo "Failure to create temporary file\n";
            break;
        case ZipArchive::ER_ZLIB:
            echo "Zlib error\n";
            break;
        case ZipArchive::ER_MEMORY:
            echo "Memory allocation failure\n";
            break;
        case ZipArchive::ER_CHANGED:
            echo "Entry has been changed\n";
            break;
        case ZipArchive::ER_COMPNOTSUPP:
            echo "Compression method not supported\n";
            break;
        case ZipArchive::ER_EOF:
            echo "Premature EOF\n";
            break;
        case ZipArchive::ER_INVAL:
            echo "Invalid argument\n";
            break;
        case ZipArchive::ER_NOZIP:
            echo "Not a zip archive\n";
            break;
        case ZipArchive::ER_INTERNAL:
            echo "Internal error\n";
            break;
        case ZipArchive::ER_INCONS:
            echo "Zip archive inconsistent\n";
            break;
        case ZipArchive::ER_REMOVE:
            echo "Can't remove file\n";
            break;
        case ZipArchive::ER_DELETED:
            echo "Entry has been deleted\n";
            break;
        default:
            echo "Unknown error ($result)\n";
    }
}

echo "Teste concluído.\n";
?>
