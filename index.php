<?php
/**
 * Advanced PHP File Explorer
 * Complete file management system with modern UI and security features
 * Features: Browse, Upload, Download, Delete, Create folders, Zip/Unzip, Search
 */

// Security Configuration
define('PASSWORD', '4shared@home'); // Change this password!
define('MAX_UPLOAD_SIZE', 50 * 1024 * 1024); // 50MB
define('ALLOWED_EXTENSIONS', ['txt', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'jpg', 'jpeg', 'png', 'gif', 'zip', 'rar', '7z', 'mp3', 'mp4', 'avi', 'mov']);
define('HIDDEN_FILES', ['.htaccess', '.htpasswd', 'index.php']);

// Error reporting
error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
ini_set('display_errors', 0);

// Start session for authentication
session_start();

// Authentication check
if (PASSWORD && !isset($_SESSION['authenticated'])) {
    if (isset($_POST['password']) && $_POST['password'] === PASSWORD) {
        $_SESSION['authenticated'] = true;
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        header('Location: ' . $_SERVER['PHP_SELF']);
        exit;
    }
    showLoginForm();
    exit;
}

// CSRF Protection
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Validate CSRF token for POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        die('CSRF token mismatch');
    }
}

// Get current directory
$currentDir = isset($_GET['dir']) ? $_GET['dir'] : '.';
$currentDir = realpath($currentDir) ?: '.';
$baseDir = realpath('.');

// Security: Prevent directory traversal
if (strpos($currentDir, $baseDir) !== 0) {
    $currentDir = $baseDir;
}

// Handle download requests first (before any output)
if (isset($_GET['action']) && $_GET['action'] === 'download' && isset($_GET['file'])) {
    // Clear any output buffers
    while (ob_get_level()) {
        ob_end_clean();
    }
    handleDownload($_GET['file']);
    exit;
}

// Handle temporary download requests
if (isset($_GET['action']) && $_GET['action'] === 'download_temp') {
    // Clear any output buffers
    while (ob_get_level()) {
        ob_end_clean();
    }
    handleTempDownload();
    exit;
}

// Handle AJAX requests
if (isset($_GET['action'])) {
    handleAjaxRequest();
    exit;
}

// Handle file operations
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    handlePostRequest();
}

function showLoginForm() {
    ?>
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Explorador de Arquivos - Login</title>
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%); margin: 0; padding: 0; height: 100vh; display: flex; align-items: center; justify-content: center; }
            .login-container { background: #1f2937; padding: 2rem; border-radius: 10px; box-shadow: 0 10px 25px rgba(0,0,0,0.3); width: 300px; border: 1px solid #374151; }
            .login-container h2 { text-align: center; margin-bottom: 1.5rem; color: #f9fafb; }
            .form-group { margin-bottom: 1rem; }
            .form-group label { display: block; margin-bottom: 0.5rem; color: #d1d5db; }
            .form-group input { width: 100%; padding: 0.75rem; border: 1px solid #4b5563; border-radius: 5px; font-size: 1rem; background: #374151; color: #f9fafb; }
            .form-group input:focus { outline: none; border-color: #3b82f6; box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2); }
            .btn { width: 100%; padding: 0.75rem; background: #3b82f6; color: white; border: none; border-radius: 5px; font-size: 1rem; cursor: pointer; transition: background 0.3s; }
            .btn:hover { background: #2563eb; }
        </style>
    </head>
    <body>
        <div class="login-container">
            <h2>🔐 Explorador de Arquivos</h2>
            <form method="post">
                <div class="form-group">
                    <label for="password">Senha:</label>
                    <input type="password" id="password" name="password" required autofocus>
                </div>
                <button type="submit" class="btn">Entrar</button>
            </form>
        </div>
    </body>
    </html>
    <?php
}

function handleAjaxRequest() {
    global $currentDir;

    switch ($_GET['action']) {
        case 'list':
            echo json_encode(getDirectoryListing($currentDir));
            break;
        case 'search':
            $query = $_GET['q'] ?? '';
            echo json_encode(searchFiles($currentDir, $query));
            break;
        case 'preview':
            $file = $_GET['file'] ?? '';
            echo json_encode(getFilePreview($file));
            break;
        default:
            echo json_encode(['error' => 'Invalid action']);
    }
}

function handleDownload($filePath) {
    // Decode the file path
    $filePath = urldecode($filePath);

    // Debug: Log the original file path
    error_log("Download request - Original path: " . $filePath);

    // Clean up the path
    $filePath = trim($filePath);
    $filePath = str_replace(['\\', '/'], DIRECTORY_SEPARATOR, $filePath);

    // Get base directory
    $baseDir = realpath('.');
    $realPath = null;

    // Strategy 1: Try as relative path from base directory
    if (strpos($filePath, './') === 0) {
        $testPath = $baseDir . DIRECTORY_SEPARATOR . substr($filePath, 2);
        if (file_exists($testPath)) {
            $realPath = realpath($testPath);
            error_log("Strategy 1 - Found relative path: " . $testPath);
        }
    }

    // Strategy 2: Try the path as-is
    if (!$realPath && file_exists($filePath)) {
        $realPath = realpath($filePath);
        error_log("Strategy 2 - Found direct path: " . $filePath);
    }

    // Strategy 3: Try as relative to base directory (without ./)
    if (!$realPath) {
        $testPath = $baseDir . DIRECTORY_SEPARATOR . ltrim($filePath, DIRECTORY_SEPARATOR);
        if (file_exists($testPath)) {
            $realPath = realpath($testPath);
            error_log("Strategy 3 - Found base relative path: " . $testPath);
        }
    }

    if (!$realPath || !file_exists($realPath)) {
        error_log("Download error: No valid path found for: " . $filePath);
        http_response_code(404);
        die('File not found');
    }

    // Security check
    $baseDir = realpath('.');
    $baseDir = str_replace('\\', '/', $baseDir);
    $realPath = str_replace('\\', '/', $realPath);

    if (strpos($realPath, $baseDir) !== 0) {
        error_log("Download error: Access denied - " . $realPath . " not in " . $baseDir);
        http_response_code(403);
        die('Access denied');
    }

    if (!is_readable($realPath)) {
        error_log("Download error: File not readable - " . $realPath);
        http_response_code(403);
        die('File not readable');
    }

    if (is_dir($realPath)) {
        // Handle directory download by creating a temporary zip file
        handleDirectoryDownload($realPath);
        exit;
    }

    $filename = basename($realPath);
    $filesize = filesize($realPath);

    error_log("Download success: " . $realPath . " (" . $filesize . " bytes)");

    // Clear any previous output
    while (ob_get_level()) {
        ob_end_clean();
    }

    // Set headers for file download
    header('Content-Type: application/octet-stream');
    header('Content-Length: ' . $filesize);
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: 0');
    header('Pragma: public');

    // Output file
    readfile($realPath);
    exit;
}

function handleDirectoryDownload($dirPath) {
    // Check if ZipArchive is available
    if (!class_exists('ZipArchive')) {
        http_response_code(500);
        die('Error: ZIP functionality not available');
    }

    $dirName = basename($dirPath);
    $tempZipName = 'temp_' . $dirName . '_' . time() . '.zip';
    $tempZipPath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . $tempZipName;

    // Create temporary zip file
    $zip = new ZipArchive();
    if ($zip->open($tempZipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
        http_response_code(500);
        die('Error: Cannot create temporary zip file');
    }

    // Add directory contents to zip
    $zip->addEmptyDir($dirName);
    if (!addDirectoryToZipRobust($zip, $dirPath, $dirName)) {
        $zip->close();
        if (file_exists($tempZipPath)) {
            unlink($tempZipPath);
        }
        http_response_code(500);
        die('Error: Failed to compress directory');
    }

    $zip->close();

    // Check if zip file was created successfully
    if (!file_exists($tempZipPath) || filesize($tempZipPath) == 0) {
        if (file_exists($tempZipPath)) {
            unlink($tempZipPath);
        }
        http_response_code(500);
        die('Error: Failed to create zip file');
    }

    $filesize = filesize($tempZipPath);
    $downloadName = $dirName . '.zip';

    // Clear any previous output
    while (ob_get_level()) {
        ob_end_clean();
    }

    // Set headers for file download
    header('Content-Type: application/zip');
    header('Content-Length: ' . $filesize);
    header('Content-Disposition: attachment; filename="' . $downloadName . '"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: 0');
    header('Pragma: public');

    // Output file and delete temporary file
    readfile($tempZipPath);
    unlink($tempZipPath);

    error_log("Directory download success: " . $dirPath . " as " . $downloadName . " (" . $filesize . " bytes)");
    exit;
}

function handleTempDownload() {
    if (!isset($_SESSION['temp_download']) || !isset($_SESSION['temp_download_name'])) {
        http_response_code(404);
        die('No temporary file available');
    }

    $tempFilePath = $_SESSION['temp_download'];
    $downloadName = $_SESSION['temp_download_name'];

    // Clear session variables
    unset($_SESSION['temp_download']);
    unset($_SESSION['temp_download_name']);

    if (!file_exists($tempFilePath)) {
        http_response_code(404);
        die('Temporary file not found');
    }

    $filesize = filesize($tempFilePath);

    // Set headers for file download
    header('Content-Type: application/zip');
    header('Content-Length: ' . $filesize);
    header('Content-Disposition: attachment; filename="' . $downloadName . '"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: 0');
    header('Pragma: public');

    // Output file and delete temporary file
    readfile($tempFilePath);
    unlink($tempFilePath);

    error_log("Temporary download success: " . $downloadName . " (" . $filesize . " bytes)");
    exit;
}

function handlePostRequest() {
    global $currentDir;
    
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'upload':
            handleFileUpload();
            break;
        case 'delete':
            handleFileDelete();
            break;
        case 'create_folder':
            handleCreateFolder();
            break;
        case 'rename':
            handleRename();
            break;
        case 'zip':
            handleZipFiles();
            break;
        case 'unzip':
            handleUnzipFile();
            break;
        case 'download_dir':
            handleDirectoryDownloadPost();
            break;
        case 'logout':
            session_destroy();
            header('Location: ' . $_SERVER['PHP_SELF']);
            exit;
    }
}

function getDirectoryListing($dir) {
    $files = [];
    $items = scandir($dir);
    $baseDir = realpath('.');

    foreach ($items as $item) {
        if ($item === '.' || $item === '..' || in_array($item, HIDDEN_FILES)) {
            continue;
        }

        $fullPath = $dir . DIRECTORY_SEPARATOR . $item;
        $stat = stat($fullPath);

        // Create relative path for download/operations
        $relativePath = $fullPath;
        if (strpos($fullPath, $baseDir) === 0) {
            $relativePath = '.' . substr($fullPath, strlen($baseDir));
            $relativePath = str_replace('\\', '/', $relativePath);
        }

        $files[] = [
            'name' => $item,
            'path' => $relativePath,
            'full_path' => $fullPath,
            'is_dir' => is_dir($fullPath),
            'size' => $stat['size'],
            'modified' => $stat['mtime'],
            'permissions' => substr(sprintf('%o', fileperms($fullPath)), -4),
            'readable' => is_readable($fullPath),
            'writable' => is_writable($fullPath)
        ];
    }
    
    // Sort: directories first, then files
    usort($files, function($a, $b) {
        if ($a['is_dir'] !== $b['is_dir']) {
            return $b['is_dir'] - $a['is_dir'];
        }
        return strcasecmp($a['name'], $b['name']);
    });
    
    return ['success' => true, 'files' => $files];
}

function searchFiles($dir, $query) {
    $results = [];

    try {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && stripos($file->getFilename(), $query) !== false) {
                $results[] = [
                    'name' => $file->getFilename(),
                    'path' => $file->getPathname(),
                    'is_dir' => false,
                    'size' => $file->getSize(),
                    'modified' => $file->getMTime(),
                    'permissions' => substr(sprintf('%o', $file->getPerms()), -4),
                    'readable' => $file->isReadable(),
                    'writable' => $file->isWritable()
                ];
            }
        }
    } catch (Exception $e) {
        return ['success' => false, 'error' => 'Search failed: ' . $e->getMessage()];
    }

    return ['success' => true, 'files' => $results];
}

function getFilePreview($file) {
    // This function can be extended to provide file previews
    // For now, just return basic file info
    if (!file_exists($file)) {
        return ['error' => 'File not found'];
    }

    $info = [
        'name' => basename($file),
        'size' => filesize($file),
        'modified' => filemtime($file),
        'type' => mime_content_type($file)
    ];

    return ['success' => true, 'info' => $info];
}

function handleFileUpload() {
    global $currentDir;
    
    if (!isset($_FILES['files'])) {
        echo json_encode(['error' => 'No files uploaded']);
        return;
    }
    
    $uploadedFiles = [];
    $errors = [];
    
    foreach ($_FILES['files']['tmp_name'] as $key => $tmpName) {
        $fileName = $_FILES['files']['name'][$key];
        $fileSize = $_FILES['files']['size'][$key];
        $fileError = $_FILES['files']['error'][$key];
        
        if ($fileError !== UPLOAD_ERR_OK) {
            $errors[] = "Error uploading $fileName";
            continue;
        }
        
        if ($fileSize > MAX_UPLOAD_SIZE) {
            $errors[] = "$fileName exceeds maximum file size";
            continue;
        }
        
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        if (!in_array($extension, ALLOWED_EXTENSIONS)) {
            $errors[] = "$fileName has an invalid file type";
            continue;
        }
        
        $destination = $currentDir . DIRECTORY_SEPARATOR . $fileName;
        if (move_uploaded_file($tmpName, $destination)) {
            $uploadedFiles[] = $fileName;
        } else {
            $errors[] = "Failed to move $fileName";
        }
    }
    
    echo json_encode([
        'success' => empty($errors),
        'uploaded' => $uploadedFiles,
        'errors' => $errors
    ]);
}

function handleFileDelete() {
    $filesJson = $_POST['files'] ?? '[]';
    $files = json_decode($filesJson, true);
    $deleted = [];
    $errors = [];

    if (!is_array($files)) {
        echo json_encode(['error' => 'Invalid files data']);
        return;
    }

    foreach ($files as $file) {
        // Normalize path separators for Windows
        $file = str_replace('\\', '/', $file);

        // Security check
        $realPath = realpath($file);
        $baseDir = realpath('.');

        if (!$realPath) {
            $errors[] = "File not found: " . basename($file);
            continue;
        }

        // Normalize paths for comparison
        $realPath = str_replace('\\', '/', $realPath);
        $baseDir = str_replace('\\', '/', $baseDir);

        if (strpos($realPath, $baseDir) !== 0) {
            $errors[] = "Access denied: " . basename($file);
            continue;
        }

        if (is_dir($realPath)) {
            if (removeDirectory($realPath)) {
                $deleted[] = basename($file);
            } else {
                $errors[] = "Failed to delete directory: " . basename($file);
            }
        } else {
            if (unlink($realPath)) {
                $deleted[] = basename($file);
            } else {
                $errors[] = "Failed to delete file: " . basename($file);
            }
        }
    }

    echo json_encode([
        'success' => empty($errors),
        'deleted' => $deleted,
        'errors' => $errors
    ]);
}

function handleCreateFolder() {
    global $currentDir;
    
    $folderName = $_POST['folder_name'] ?? '';
    $folderName = preg_replace('/[^a-zA-Z0-9_\-\s]/', '', $folderName);
    
    if (empty($folderName)) {
        echo json_encode(['error' => 'Invalid folder name']);
        return;
    }
    
    $folderPath = $currentDir . DIRECTORY_SEPARATOR . $folderName;
    
    if (mkdir($folderPath, 0755)) {
        echo json_encode(['success' => true, 'folder' => $folderName]);
    } else {
        echo json_encode(['error' => 'Failed to create folder']);
    }
}

function handleZipFiles() {
    global $currentDir;

    // Clear any output buffers to prevent HTML/whitespace before JSON
    while (ob_get_level()) {
        ob_end_clean();
    }

    $filesJson = $_POST['files'] ?? '[]';
    $files = json_decode($filesJson, true);
    $zipName = $_POST['zip_name'] ?? 'archive.zip';

    if (!is_array($files) || empty($files)) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'No files selected']);
        return;
    }

    // Ensure zip extension (compatible with older PHP versions)
    if (substr(strtolower($zipName), -4) !== '.zip') {
        $zipName .= '.zip';
    }

    // Check if we have zip functionality available
    $hasZipArchive = class_exists('ZipArchive');
    $hasPharData = class_exists('PharData');
    $hasSystemZip = false;

    // Check if system zip command is available
    if (function_exists('exec')) {
        $output = [];
        $return_var = 0;
        @exec('zip --version 2>&1', $output, $return_var);
        $hasSystemZip = ($return_var === 0);
    }

    if (!$hasZipArchive && !$hasPharData && !$hasSystemZip) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Error: No zip functionality available. Please install php-zip extension or zip command.']);
        return;
    }

    // Debug logging
    error_log("Zip request - Files: " . print_r($files, true));
    error_log("Zip request - Current dir: " . $currentDir);
    error_log("Zip request - Zip name: " . $zipName);

    $zip = new ZipArchive();

    // Determine zip path - use current directory if files are from different locations
    $zipPath = $currentDir . DIRECTORY_SEPARATOR . $zipName;

    // Check if directory is writable
    if (!is_writable($currentDir)) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Directory is not writable: ' . $currentDir]);
        return;
    }

    // Ensure unique filename if file already exists
    $counter = 1;
    $originalZipPath = $zipPath;
    while (file_exists($zipPath)) {
        $pathInfo = pathinfo($originalZipPath);
        $zipPath = $pathInfo['dirname'] . DIRECTORY_SEPARATOR .
                   $pathInfo['filename'] . '_' . $counter . '.' . $pathInfo['extension'];
        $counter++;
    }

    $zipName = basename($zipPath);

    error_log("Attempting to create zip at: " . $zipPath);

    // Try different compression methods
    $result = createZipFile($zipPath, $files, $hasZipArchive, $hasPharData, $hasSystemZip);

    if ($result['success']) {

        if ($addedFiles > 0) {
            $message = "Archive '$zipName' created successfully with $addedFiles items";
            if (!empty($errors)) {
                $message .= ". Warnings: " . implode(', ', $errors);
            }
            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'zip' => $zipName, 'message' => $message]);
        } else {
            // Remove empty zip file
            if (file_exists($zipPath)) {
                unlink($zipPath);
            }
            header('Content-Type: application/json');
            echo json_encode(['error' => 'No files could be added to archive. ' . implode(', ', $errors)]);
        }
    } else {
        // Get more specific error information
        $errorMsg = 'Failed to create zip file';
        switch ($zipResult) {
            case ZipArchive::ER_OK:
                $errorMsg .= ': No error';
                break;
            case ZipArchive::ER_MULTIDISK:
                $errorMsg .= ': Multi-disk zip archives not supported';
                break;
            case ZipArchive::ER_RENAME:
                $errorMsg .= ': Renaming temporary file failed';
                break;
            case ZipArchive::ER_CLOSE:
                $errorMsg .= ': Closing zip archive failed';
                break;
            case ZipArchive::ER_SEEK:
                $errorMsg .= ': Seek error';
                break;
            case ZipArchive::ER_READ:
                $errorMsg .= ': Read error';
                break;
            case ZipArchive::ER_WRITE:
                $errorMsg .= ': Write error';
                break;
            case ZipArchive::ER_CRC:
                $errorMsg .= ': CRC error';
                break;
            case ZipArchive::ER_ZIPCLOSED:
                $errorMsg .= ': Containing zip archive was closed';
                break;
            case ZipArchive::ER_NOENT:
                $errorMsg .= ': No such file';
                break;
            case ZipArchive::ER_EXISTS:
                $errorMsg .= ': File already exists';
                break;
            case ZipArchive::ER_OPEN:
                $errorMsg .= ': Can\'t open file';
                break;
            case ZipArchive::ER_TMPOPEN:
                $errorMsg .= ': Failure to create temporary file';
                break;
            case ZipArchive::ER_ZLIB:
                $errorMsg .= ': Zlib error';
                break;
            case ZipArchive::ER_MEMORY:
                $errorMsg .= ': Memory allocation failure';
                break;
            case ZipArchive::ER_CHANGED:
                $errorMsg .= ': Entry has been changed';
                break;
            case ZipArchive::ER_COMPNOTSUPP:
                $errorMsg .= ': Compression method not supported';
                break;
            case ZipArchive::ER_EOF:
                $errorMsg .= ': Premature EOF';
                break;
            case ZipArchive::ER_INVAL:
                $errorMsg .= ': Invalid argument';
                break;
            case ZipArchive::ER_NOZIP:
                $errorMsg .= ': Not a zip archive';
                break;
            case ZipArchive::ER_INTERNAL:
                $errorMsg .= ': Internal error';
                break;
            case ZipArchive::ER_INCONS:
                $errorMsg .= ': Zip archive inconsistent';
                break;
            case ZipArchive::ER_REMOVE:
                $errorMsg .= ': Can\'t remove file';
                break;
            case ZipArchive::ER_DELETED:
                $errorMsg .= ': Entry has been deleted';
                break;
            default:
                $errorMsg .= ': Unknown error (' . $zipResult . ')';
        }

        error_log("Zip creation failed: " . $errorMsg . " for path: " . $zipPath);
        header('Content-Type: application/json');
        echo json_encode(['error' => $errorMsg]);
    }
}

function handleUnzipFile() {
    $zipFile = $_POST['zip_file'] ?? '';
    $extractTo = $_POST['extract_to'] ?? '';

    if (empty($zipFile)) {
        echo json_encode(['error' => 'No zip file specified']);
        return;
    }

    // Security check - ensure zip file exists and is within allowed directory
    $realZipPath = realpath($zipFile);
    if (!$realZipPath || !file_exists($realZipPath)) {
        echo json_encode(['error' => 'Zip file not found']);
        return;
    }

    $baseDir = realpath('.');
    $realZipPath = str_replace('\\', '/', $realZipPath);
    $baseDir = str_replace('\\', '/', $baseDir);

    if (strpos($realZipPath, $baseDir) !== 0) {
        echo json_encode(['error' => 'Access denied']);
        return;
    }

    // Determine extraction path
    if (empty($extractTo)) {
        $extractTo = dirname($realZipPath);
    } else {
        // Security check for extraction path
        $realExtractPath = realpath($extractTo);
        if (!$realExtractPath) {
            // Try to create the directory if it doesn't exist
            if (!mkdir($extractTo, 0755, true)) {
                echo json_encode(['error' => 'Cannot create extraction directory']);
                return;
            }
            $realExtractPath = realpath($extractTo);
        }

        $realExtractPath = str_replace('\\', '/', $realExtractPath);
        if (strpos($realExtractPath, $baseDir) !== 0) {
            echo json_encode(['error' => 'Extraction path access denied']);
            return;
        }
        $extractTo = $realExtractPath;
    }

    // Check if extraction directory is writable
    if (!is_writable($extractTo)) {
        echo json_encode(['error' => 'Extraction directory is not writable']);
        return;
    }

    // Check file extension and extract accordingly
    $ext = strtolower(pathinfo($realZipPath, PATHINFO_EXTENSION));

    switch ($ext) {
        case 'zip':
            $result = extractZipArchive($realZipPath, $extractTo);
            break;
        case 'gz':
            $result = extractGzipFile($realZipPath, $extractTo);
            break;
        case 'rar':
            $result = extractRarArchive($realZipPath, $extractTo);
            break;
        default:
            echo json_encode(['error' => 'Unsupported archive format']);
            return;
    }

    echo json_encode($result);
}

function handleDirectoryDownloadPost() {
    // Clear any output buffers to prevent HTML/whitespace before JSON
    while (ob_get_level()) {
        ob_end_clean();
    }

    $dirPath = $_POST['dir_path'] ?? '';

    if (empty($dirPath)) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'No directory specified']);
        return;
    }

    // Security check
    $realPath = realpath($dirPath);
    if (!$realPath || !is_dir($realPath)) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Directory not found']);
        return;
    }

    $baseDir = realpath('.');
    $realPath = str_replace('\\', '/', $realPath);
    $baseDir = str_replace('\\', '/', $baseDir);

    if (strpos($realPath, $baseDir) !== 0) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Access denied']);
        return;
    }

    // Instead of returning a URL, directly create and serve the zip file
    try {
        // Check if ZipArchive is available
        if (!class_exists('ZipArchive')) {
            header('Content-Type: application/json');
            echo json_encode(['error' => 'ZIP functionality not available']);
            return;
        }

        $dirName = basename($realPath);
        $tempZipName = 'temp_' . $dirName . '_' . time() . '.zip';
        $tempZipPath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . $tempZipName;

        // Create temporary zip file
        $zip = new ZipArchive();
        if ($zip->open($tempZipPath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== TRUE) {
            header('Content-Type: application/json');
            echo json_encode(['error' => 'Cannot create temporary zip file']);
            return;
        }

        // Add directory contents to zip
        $zip->addEmptyDir($dirName);
        if (!addDirectoryToZipRobust($zip, $realPath, $dirName)) {
            $zip->close();
            if (file_exists($tempZipPath)) {
                unlink($tempZipPath);
            }
            header('Content-Type: application/json');
            echo json_encode(['error' => 'Failed to compress directory']);
            return;
        }

        $zip->close();

        // Check if zip file was created successfully
        if (!file_exists($tempZipPath) || filesize($tempZipPath) == 0) {
            if (file_exists($tempZipPath)) {
                unlink($tempZipPath);
            }
            header('Content-Type: application/json');
            echo json_encode(['error' => 'Failed to create zip file']);
            return;
        }

        // Store the temp file path in session for download
        $_SESSION['temp_download'] = $tempZipPath;
        $_SESSION['temp_download_name'] = $dirName . '.zip';

        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'download_url' => '?action=download_temp']);

    } catch (Exception $e) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Error creating zip: ' . $e->getMessage()]);
    }
}

function extractZipArchive($archive, $destination) {
    // Check if webserver supports unzipping
    if (!class_exists('ZipArchive')) {
        return ['error' => 'Error: Your PHP version does not support unzip functionality.'];
    }

    $zip = new ZipArchive;

    // Check if archive is readable
    if ($zip->open($archive) === TRUE) {
        // Check if destination is writable
        if (is_writeable($destination . '/')) {
            if ($zip->extractTo($destination)) {
                $zip->close();
                return ['success' => true, 'message' => 'Files extracted successfully'];
            } else {
                $zip->close();
                return ['error' => 'Error: Failed to extract files'];
            }
        } else {
            $zip->close();
            return ['error' => 'Error: Directory not writeable by webserver.'];
        }
    } else {
        return ['error' => 'Error: Cannot read .zip archive.'];
    }
}

function extractGzipFile($archive, $destination) {
    // Check if zlib is enabled
    if (!function_exists('gzopen')) {
        return ['error' => 'Error: Your PHP has no zlib support enabled.'];
    }

    $filename = pathinfo($archive, PATHINFO_FILENAME);
    $gzipped = gzopen($archive, "rb");

    if (!$gzipped) {
        return ['error' => 'Error: Cannot open .gz file.'];
    }

    $file = fopen($destination . '/' . $filename, "w");

    if (!$file) {
        gzclose($gzipped);
        return ['error' => 'Error: Cannot create destination file.'];
    }

    while ($string = gzread($gzipped, 4096)) {
        fwrite($file, $string, strlen($string));
    }
    gzclose($gzipped);
    fclose($file);

    // Check if file was extracted
    if (file_exists($destination . '/' . $filename)) {
        $result = ['success' => true, 'message' => 'File extracted successfully.'];

        // If we had a tar.gz file, let's extract that tar file
        if (pathinfo($destination . '/' . $filename, PATHINFO_EXTENSION) == 'tar') {
            try {
                $phar = new PharData($destination . '/' . $filename);
                if ($phar->extractTo($destination)) {
                    $result['message'] = 'Extracted tar.gz archive successfully.';
                    // Delete .tar file
                    unlink($destination . '/' . $filename);
                }
            } catch (Exception $e) {
                $result['message'] .= ' Warning: Could not extract tar contents.';
            }
        }
        return $result;
    } else {
        return ['error' => 'Error extracting file.'];
    }
}

function extractRarArchive($archive, $destination) {
    // Check if webserver supports rar extraction
    if (!class_exists('RarArchive')) {
        return ['error' => 'Error: Your PHP version does not support .rar archive functionality.'];
    }

    // Check if archive is readable
    if ($rar = RarArchive::open($archive)) {
        // Check if destination is writable
        if (is_writeable($destination . '/')) {
            $entries = $rar->getEntries();
            if ($entries !== false) {
                foreach ($entries as $entry) {
                    $entry->extract($destination);
                }
                $rar->close();
                return ['success' => true, 'message' => 'Files extracted successfully.'];
            } else {
                $rar->close();
                return ['error' => 'Error: Cannot read archive entries.'];
            }
        } else {
            $rar->close();
            return ['error' => 'Error: Directory not writeable by webserver.'];
        }
    } else {
        return ['error' => 'Error: Cannot read .rar archive.'];
    }
}

function removeDirectory($dir) {
    if (!is_dir($dir)) return false;
    
    $files = array_diff(scandir($dir), ['.', '..']);
    foreach ($files as $file) {
        $path = $dir . DIRECTORY_SEPARATOR . $file;
        is_dir($path) ? removeDirectory($path) : unlink($path);
    }
    return rmdir($dir);
}

function addDirectoryToZip($zip, $dir, $zipDir) {
    $files = scandir($dir);
    foreach ($files as $file) {
        if ($file === '.' || $file === '..') continue;

        $filePath = $dir . DIRECTORY_SEPARATOR . $file;
        $zipPath = $zipDir . DIRECTORY_SEPARATOR . $file;

        if (is_dir($filePath)) {
            $zip->addEmptyDir($zipPath);
            addDirectoryToZip($zip, $filePath, $zipPath);
        } else {
            $zip->addFile($filePath, $zipPath);
        }
    }
}

function addDirectoryToZipRobust($zip, $dir, $zipDir) {
    if (!is_dir($dir) || !is_readable($dir)) {
        return false;
    }

    $handle = opendir($dir);
    if (!$handle) {
        return false;
    }

    $success = true;

    while (FALSE !== ($file = readdir($handle))) {
        // Skip current and parent directory references
        if ($file === '.' || $file === '..' || $file === basename(__FILE__)) {
            continue;
        }

        $filePath = $dir . DIRECTORY_SEPARATOR . $file;
        $zipPath = $zipDir . DIRECTORY_SEPARATOR . $file;

        // Security check - skip hidden files that might be sensitive
        if (in_array($file, HIDDEN_FILES)) {
            continue;
        }

        if (is_file($filePath) && is_readable($filePath)) {
            if (!$zip->addFile($filePath, $zipPath)) {
                $success = false;
            }
        } elseif (is_dir($filePath) && is_readable($filePath)) {
            // Add empty directory first
            if ($zip->addEmptyDir($zipPath)) {
                // Recursively add directory contents
                if (!addDirectoryToZipRobust($zip, $filePath, $zipPath)) {
                    $success = false;
                }
            } else {
                $success = false;
            }
        }
    }

    closedir($handle);
    return $success;
}

function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}

function getFileIcon($filename, $isDir) {
    if ($isDir) return '📁';
    
    $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    $icons = [
        'txt' => '📄', 'pdf' => '📕', 'doc' => '📘', 'docx' => '📘',
        'xls' => '📗', 'xlsx' => '📗', 'ppt' => '📙', 'pptx' => '📙',
        'jpg' => '🖼️', 'jpeg' => '🖼️', 'png' => '🖼️', 'gif' => '🖼️',
        'zip' => '🗜️', 'rar' => '🗜️', '7z' => '🗜️',
        'mp3' => '🎵', 'mp4' => '🎬', 'avi' => '🎬', 'mov' => '🎬'
    ];
    
    return $icons[$ext] ?? '📄';
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🗂️ Explorador de Arquivos Avançado</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0f172a;
            min-height: 100vh;
            color: #f1f5f9;
        }

        .header {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .nav-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .nav-btn {
            padding: 0.5rem;
            background: #374151;
            color: #d1d5db;
            border: 1px solid #4b5563;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 0.9rem;
            min-width: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-btn:hover:not(:disabled) {
            background: #4b5563;
            color: #f9fafb;
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary { background: #10b981; color: white; }
        .btn-primary:hover { background: #059669; }

        .btn-secondary { background: #3b82f6; color: white; }
        .btn-secondary:hover { background: #2563eb; }

        .btn-danger { background: #ef4444; color: white; }
        .btn-danger:hover { background: #dc2626; }

        .btn-warning { background: #f59e0b; color: white; }
        .btn-warning:hover { background: #d97706; }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .toolbar {
            background: #1e293b;
            padding: 1rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            margin-bottom: 1rem;
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            align-items: center;
            border: 1px solid #334155;
        }

        .search-box {
            display: flex;
            gap: 0.5rem;
        }

        .search-box input {
            padding: 0.5rem;
            border: 1px solid #4b5563;
            border-radius: 5px;
            font-size: 0.9rem;
            width: 200px;
            background: #374151;
            color: #f9fafb;
        }

        .search-box input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
        }

        .search-box input::placeholder {
            color: #9ca3af;
        }

        .search-box {
            display: flex;
            gap: 0.5rem;
        }

        .search-box input {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 0.9rem;
            width: 200px;
        }

        .path-navigation {
            background: #1e293b;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            margin-bottom: 1rem;
            padding: 1rem;
            border: 1px solid #334155;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .path-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .path-breadcrumb {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            background: #374151;
            border: 1px solid #4b5563;
            border-radius: 5px;
            min-height: 2.5rem;
            cursor: pointer;
            transition: all 0.3s;
        }

        .path-breadcrumb:hover {
            background: #4b5563;
        }

        .path-breadcrumb .path-segment {
            color: #3b82f6;
            text-decoration: none;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            transition: all 0.3s;
            cursor: pointer;
        }

        .path-breadcrumb .path-segment:hover {
            background: #1e40af;
            color: white;
        }

        .path-breadcrumb .path-separator {
            color: #94a3b8;
            margin: 0 0.25rem;
        }

        .path-edit {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .path-input {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid #4b5563;
            border-radius: 5px;
            background: #374151;
            color: #f9fafb;
            font-size: 0.9rem;
        }

        .path-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
        }

        .file-grid {
            background: #1e293b;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            overflow: hidden;
            border: 1px solid #334155;
        }

        .file-grid-header {
            background: #334155;
            padding: 1rem;
            border-bottom: 1px solid #475569;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .file-grid-header label {
            color: #d1d5db;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .view-toggle {
            display: flex;
            gap: 0.5rem;
        }

        .view-toggle button {
            padding: 0.5rem;
            border: 1px solid #4b5563;
            background: #374151;
            color: #d1d5db;
            cursor: pointer;
            border-radius: 3px;
            transition: all 0.3s;
        }

        .view-toggle button.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .view-toggle button:hover:not(.active) {
            background: #4b5563;
        }

        .file-list {
            max-height: 600px;
            overflow-y: auto;
        }

        .file-list::-webkit-scrollbar {
            width: 8px;
        }

        .file-list::-webkit-scrollbar-track {
            background: #1e293b;
        }

        .file-list::-webkit-scrollbar-thumb {
            background: #475569;
            border-radius: 4px;
        }

        .file-list::-webkit-scrollbar-thumb:hover {
            background: #64748b;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #334155;
            cursor: pointer;
            transition: background 0.3s;
        }

        .file-item:hover {
            background: #334155;
        }

        .file-item.selected {
            background: #1e40af;
        }

        .file-item input[type="checkbox"] {
            margin-right: 1rem;
            accent-color: #3b82f6;
        }

        .file-icon {
            font-size: 1.5rem;
            margin-right: 1rem;
            width: 2rem;
            text-align: center;
        }

        .file-info {
            flex: 1;
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr;
            gap: 1rem;
            align-items: center;
        }

        .file-name {
            font-weight: 500;
            color: #f1f5f9;
        }

        .file-size, .file-date, .file-permissions {
            font-size: 0.85rem;
            color: #94a3b8;
        }

        .file-actions {
            display: flex;
            gap: 0.5rem;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .file-item:hover .file-actions {
            opacity: 1;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #1e293b;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.5);
            max-width: 500px;
            width: 90%;
            border: 1px solid #334155;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .modal-header h3 {
            margin: 0;
            color: #f1f5f9;
        }

        .close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #94a3b8;
            transition: color 0.3s;
        }

        .close:hover {
            color: #f1f5f9;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #d1d5db;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #4b5563;
            border-radius: 5px;
            font-size: 1rem;
            background: #374151;
            color: #f9fafb;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
        }

        .upload-area {
            border: 2px dashed #4b5563;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            background: #374151;
            color: #d1d5db;
        }

        .upload-area:hover, .upload-area.dragover {
            border-color: #3b82f6;
            background: #1e40af;
            color: #f1f5f9;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #374151;
            border-radius: 10px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            width: 0%;
            transition: width 0.3s;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 5px;
            color: white;
            font-weight: 500;
            z-index: 1001;
            transform: translateX(400px);
            transition: transform 0.3s;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success { background: #10b981; }
        .notification.error { background: #ef4444; }
        .notification.warning { background: #f59e0b; }
        .notification.info { background: #3b82f6; }

        @media (max-width: 768px) {
            .container { padding: 1rem; }
            .path-navigation { flex-direction: column; gap: 0.5rem; }
            .path-buttons { order: 2; }
            .path-breadcrumb { order: 1; }
            .toolbar { flex-direction: column; align-items: stretch; }
            .file-info { grid-template-columns: 1fr; gap: 0.5rem; }
            .search-box input { width: 100%; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🗂️ Explorador de Arquivos Avançado</h1>
        <div class="header-actions">
            <span>Bem-vindo! Pasta atual: <?php echo basename($currentDir); ?></span>
            <form method="post" style="display: inline;">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="action" value="logout">
                <button type="submit" class="btn btn-danger">🚪 Sair</button>
            </form>
        </div>
    </div>



    <div class="container">
        <div class="toolbar">
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="Pesquisar arquivos...">
                <button class="btn btn-secondary" onclick="searchFiles()">🔍 Pesquisar</button>
            </div>

            <button class="btn btn-primary" onclick="showUploadModal()">📤 Enviar</button>
            <button class="btn btn-primary" onclick="showCreateFolderModal()">📁 Nova Pasta</button>
            <button class="btn btn-warning" onclick="zipSelected()" id="zipBtn" disabled>🗜️ Compactar</button>
            <button class="btn btn-danger" onclick="deleteSelected()" id="deleteBtn" disabled>🗑️ Excluir</button>
        </div>

        <!-- Nova barra de endereço navegável -->
        <div class="path-navigation">
            <div class="path-buttons">
                <button class="nav-btn" id="backBtn" onclick="navigateBack()" title="Voltar" disabled>◀</button>
                <button class="nav-btn" id="forwardBtn" onclick="navigateForward()" title="Avançar" disabled>▶</button>
                <button class="nav-btn" onclick="navigateUp()" title="Nível acima" id="upBtn">⬆️</button>
                <button class="nav-btn" onclick="refreshFiles()" title="Atualizar">🔄</button>
            </div>
            <div class="path-breadcrumb" id="pathBreadcrumb">
                <!-- Breadcrumb será preenchido pelo JavaScript -->
            </div>
            <div class="path-edit" id="pathEdit" style="display: none;">
                <input type="text" id="pathEditInput" class="path-input">
                <button class="nav-btn" onclick="confirmPathEdit()">✓</button>
                <button class="nav-btn" onclick="cancelPathEdit()">✗</button>
            </div>
        </div>

        <div class="file-grid">
            <div class="file-grid-header">
                <div>
                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                    <label for="selectAll">Selecionar Todos</label>
                </div>
                <div class="view-toggle">
                    <button class="active" onclick="setView('list')">📋 Lista</button>
                    <button onclick="setView('grid')">⊞ Grade</button>
                </div>
            </div>

            <div class="file-list" id="fileList">
                <!-- Files will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Upload Modal -->
    <div id="uploadModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>📤 Enviar Arquivos</h3>
                <button class="close" onclick="closeModal('uploadModal')">&times;</button>
            </div>
            <form id="uploadForm" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="action" value="upload">

                <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                    <p>📁 Clique aqui ou arraste arquivos para enviar</p>
                    <p><small>Tamanho máximo: <?php echo formatFileSize(MAX_UPLOAD_SIZE); ?></small></p>
                    <input type="file" id="fileInput" name="files[]" multiple style="display: none;">
                </div>

                <div id="uploadProgress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <p id="progressText">Enviando...</p>
                </div>

                <div style="margin-top: 1rem; text-align: right;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('uploadModal')">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Enviar</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Create Folder Modal -->
    <div id="createFolderModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>📁 Criar Nova Pasta</h3>
                <button class="close" onclick="closeModal('createFolderModal')">&times;</button>
            </div>
            <form id="createFolderForm">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="action" value="create_folder">

                <div class="form-group">
                    <label for="folderName">Nome da Pasta:</label>
                    <input type="text" id="folderName" name="folder_name" required>
                </div>

                <div style="text-align: right;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('createFolderModal')">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Criar</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Zip Modal -->
    <div id="zipModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🗜️ Criar Arquivo Compactado</h3>
                <button class="close" onclick="closeModal('zipModal')">&times;</button>
            </div>
            <form id="zipForm">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="action" value="zip">
                <input type="hidden" id="zipFiles" name="files">

                <div class="form-group">
                    <label for="zipName">Nome do Arquivo:</label>
                    <input type="text" id="zipName" name="zip_name" value="arquivo.zip" required>
                </div>

                <div style="text-align: right;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('zipModal')">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Criar Arquivo</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let currentDir = '<?php echo addslashes($currentDir); ?>';
        let baseDir = '<?php echo addslashes(realpath('.')); ?>';
        let selectedFiles = new Set();
        let allFiles = [];
        let navigationHistory = [currentDir];
        let historyIndex = 0;

        // Initialize the file explorer
        document.addEventListener('DOMContentLoaded', function() {
            loadFiles();
            setupEventListeners();
            updateNavigationButtons();
        });

        function setupEventListeners() {
            // Upload form
            document.getElementById('uploadForm').addEventListener('submit', handleUpload);

            // Create folder form
            document.getElementById('createFolderForm').addEventListener('submit', handleCreateFolder);

            // Zip form
            document.getElementById('zipForm').addEventListener('submit', handleZip);

            // File input change
            document.getElementById('fileInput').addEventListener('change', function() {
                const files = Array.from(this.files);
                updateUploadArea(files);
            });

            // Drag and drop
            const uploadArea = document.querySelector('.upload-area');
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);

            // Search on Enter key
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchFiles();
                }
            });



            // Path breadcrumb events
            const pathBreadcrumb = document.getElementById('pathBreadcrumb');
            const pathEditInput = document.getElementById('pathEditInput');

            if (pathBreadcrumb) {
                // Double click to edit
                pathBreadcrumb.addEventListener('dblclick', startPathEdit);

                // Click on empty space to edit
                pathBreadcrumb.addEventListener('click', function(e) {
                    if (e.target === pathBreadcrumb) {
                        startPathEdit();
                    }
                });
            }

            if (pathEditInput) {
                // Enter to confirm edit
                pathEditInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        confirmPathEdit();
                    } else if (e.key === 'Escape') {
                        cancelPathEdit();
                    }
                });

                // Blur to cancel edit
                pathEditInput.addEventListener('blur', function() {
                    setTimeout(cancelPathEdit, 100); // Delay to allow button clicks
                });
            }
        }

        function updateNavigationButtons() {
            document.getElementById('backBtn').disabled = historyIndex <= 0;
            document.getElementById('forwardBtn').disabled = historyIndex >= navigationHistory.length - 1;
        }

        function navigateBack() {
            if (historyIndex > 0) {
                historyIndex--;
                currentDir = navigationHistory[historyIndex];
                loadFiles();
                updateNavigationButtons();
            }
        }

        function navigateForward() {
            if (historyIndex < navigationHistory.length - 1) {
                historyIndex++;
                currentDir = navigationHistory[historyIndex];
                loadFiles();
                updateNavigationButtons();
            }
        }



        function refreshFiles() {
            loadFiles();
            showNotification('Lista atualizada', 'info');
        }

        function loadFiles() {
            fetch(`?action=list&dir=${encodeURIComponent(currentDir)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        allFiles = data.files;
                        displayFiles(data.files);
                        updateBreadcrumb();
                    } else {
                        showNotification('Erro ao carregar arquivos', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Erro ao carregar arquivos', 'error');
                });
        }

        function displayFiles(files) {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';

            if (files.length === 0) {
                fileList.innerHTML = '<div style="text-align: center; padding: 2rem; color: #94a3b8;">📂 Esta pasta está vazia</div>';
                return;
            }

            files.forEach(file => {
                const fileItem = createFileItem(file);
                fileList.appendChild(fileItem);
            });

            updateSelectionButtons();
        }

        function createFileItem(file) {
            const item = document.createElement('div');
            item.className = 'file-item';
            item.dataset.path = file.path;

            const icon = getFileIcon(file.name, file.is_dir);
            const size = file.is_dir ? '--' : formatFileSize(file.size);
            const date = new Date(file.modified * 1000).toLocaleDateString();

            // Escape path for safe HTML insertion
            const escapedPath = file.path.replace(/'/g, "\\'").replace(/"/g, '\\"');

            item.innerHTML = `
                <input type="checkbox" onchange="toggleFileSelection('${escapedPath}')">
                <div class="file-icon">${icon}</div>
                <div class="file-info">
                    <div class="file-name">${file.name}</div>
                    <div class="file-size">${size}</div>
                    <div class="file-date">${date}</div>
                    <div class="file-permissions">${file.permissions}</div>
                </div>
                <div class="file-actions">
                    ${file.is_dir ? '<button class="btn btn-secondary" onclick="downloadDirectory(\'' + escapedPath + '\')" title="Baixar como ZIP">📦⬇️</button>' : '<button class="btn btn-secondary" onclick="downloadFile(\'' + escapedPath + '\')" title="Baixar">⬇️</button>'}
                    ${file.name.endsWith('.zip') ? '<button class="btn btn-warning" onclick="unzipFile(\'' + escapedPath + '\')" title="Extrair">📦</button>' : ''}
                    <button class="btn btn-danger" onclick="deleteFile('${escapedPath}')" title="Excluir">🗑️</button>
                </div>
            `;

            // Double click to navigate or download
            item.addEventListener('dblclick', () => {
                if (file.is_dir) {
                    navigateToDirectory(file.path);
                } else {
                    downloadFile(file.path);
                }
            });

            return item;
        }

        function getFileIcon(filename, isDir) {
            if (isDir) return '📁';

            const ext = filename.split('.').pop().toLowerCase();
            const icons = {
                'txt': '📄', 'pdf': '📕', 'doc': '📘', 'docx': '📘',
                'xls': '📗', 'xlsx': '📗', 'ppt': '📙', 'pptx': '📙',
                'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️',
                'zip': '🗜️', 'rar': '🗜️', '7z': '🗜️',
                'mp3': '🎵', 'mp4': '🎬', 'avi': '🎬', 'mov': '🎬'
            };

            return icons[ext] || '📄';
        }

        function formatFileSize(bytes) {
            const units = ['B', 'KB', 'MB', 'GB', 'TB'];
            let size = bytes;
            let unitIndex = 0;

            while (size >= 1024 && unitIndex < units.length - 1) {
                size /= 1024;
                unitIndex++;
            }

            return Math.round(size * 100) / 100 + ' ' + units[unitIndex];
        }

        function updateBreadcrumb() {
            const breadcrumb = document.getElementById('pathBreadcrumb');
            const upBtn = document.getElementById('upBtn');

            if (!breadcrumb) return;

            // Obter caminho relativo ocultando o caminho absoluto do PC
            let relativePath = getRelativePath(currentDir);

            const parts = relativePath.split('/').filter(part => part !== '' && part !== '.');

            let html = '<span class="path-segment" onclick="navigateToDirectory(\'.\')" title="Diretório inicial">🏠 Início</span>';
            let path = '.';

            parts.forEach((part, index) => {
                if (part !== '.') {
                    path += '/' + part;
                    html += '<span class="path-separator">▶</span>';
                    html += `<span class="path-segment" onclick="navigateToDirectory('${path.replace(/'/g, "\\'")}'))" title="${part}">${part}</span>`;
                }
            });

            breadcrumb.innerHTML = html;

            // Habilitar/desabilitar botão "nível acima"
            upBtn.disabled = (currentDir === '.' || currentDir === '' || currentDir === '/' || relativePath === '');
        }

        function getRelativePath(fullPath) {
            // Normalizar separadores
            let normalized = fullPath.replace(/\\/g, '/');
            let normalizedBase = baseDir.replace(/\\/g, '/');

            // Se for o diretório base, retornar vazio
            if (normalized === normalizedBase || normalized === '.') {
                return '';
            }

            // Se o caminho atual contém o diretório base, remover a parte base
            if (normalized.startsWith(normalizedBase)) {
                let relative = normalized.substring(normalizedBase.length);
                // Remover barra inicial se existir
                if (relative.startsWith('/')) {
                    relative = relative.substring(1);
                }
                return relative;
            }

            // Fallback: tentar extrair apenas as partes após o diretório base
            const parts = normalized.split('/');
            const baseParts = normalizedBase.split('/');

            // Encontrar onde o caminho diverge do base
            let divergeIndex = -1;
            for (let i = 0; i < Math.min(parts.length, baseParts.length); i++) {
                if (parts[i] !== baseParts[i]) {
                    divergeIndex = i;
                    break;
                }
            }

            if (divergeIndex === -1) {
                // Caminhos são idênticos até onde um termina
                if (parts.length > baseParts.length) {
                    return parts.slice(baseParts.length).join('/');
                }
                return '';
            }

            // Retornar apenas as partes que diferem
            return parts.slice(divergeIndex).join('/');
        }

        function navigateUp() {
            const parentDir = currentDir.split('/').slice(0, -1).join('/') || '.';
            if (parentDir !== currentDir) {
                navigateToDirectory(parentDir);
            }
        }

        function startPathEdit() {
            const breadcrumb = document.getElementById('pathBreadcrumb');
            const pathEdit = document.getElementById('pathEdit');
            const pathInput = document.getElementById('pathEditInput');

            breadcrumb.style.display = 'none';
            pathEdit.style.display = 'flex';
            pathInput.value = currentDir;
            pathInput.focus();
            pathInput.select();
        }

        function confirmPathEdit() {
            const pathInput = document.getElementById('pathEditInput');
            const newPath = pathInput.value.trim();

            if (newPath && newPath !== currentDir) {
                navigateToDirectory(newPath);
            } else {
                cancelPathEdit();
            }
        }

        function cancelPathEdit() {
            const breadcrumb = document.getElementById('pathBreadcrumb');
            const pathEdit = document.getElementById('pathEdit');

            breadcrumb.style.display = 'flex';
            pathEdit.style.display = 'none';
        }

        function navigateToDirectory(path) {
            // Add to history if it's a new navigation (not back/forward)
            if (path !== currentDir) {
                // Remove any forward history
                navigationHistory = navigationHistory.slice(0, historyIndex + 1);
                navigationHistory.push(path);
                historyIndex = navigationHistory.length - 1;
            }

            currentDir = path;
            window.location.href = `?dir=${encodeURIComponent(path)}`;
        }

        function toggleFileSelection(path) {
            if (selectedFiles.has(path)) {
                selectedFiles.delete(path);
            } else {
                selectedFiles.add(path);
            }
            updateSelectionButtons();
        }

        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.file-item input[type="checkbox"]');

            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
                const path = checkbox.closest('.file-item').dataset.path;
                if (selectAll.checked) {
                    selectedFiles.add(path);
                } else {
                    selectedFiles.delete(path);
                }
            });

            updateSelectionButtons();
        }

        function updateSelectionButtons() {
            const hasSelection = selectedFiles.size > 0;
            document.getElementById('zipBtn').disabled = !hasSelection;
            document.getElementById('deleteBtn').disabled = !hasSelection;
        }

        function searchFiles() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) {
                displayFiles(allFiles);
                return;
            }

            const filteredFiles = allFiles.filter(file =>
                file.name.toLowerCase().includes(query.toLowerCase())
            );

            displayFiles(filteredFiles);
        }

        function showUploadModal() {
            document.getElementById('uploadModal').style.display = 'block';
        }

        function showCreateFolderModal() {
            document.getElementById('createFolderModal').style.display = 'block';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function handleUpload(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            const progressDiv = document.getElementById('uploadProgress');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            progressDiv.style.display = 'block';

            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(`${data.uploaded.length} arquivo(s) enviado(s) com sucesso`, 'success');
                    closeModal('uploadModal');
                    loadFiles();
                } else {
                    showNotification('Falha no envio: ' + (data.errors ? data.errors.join(', ') : 'Erro desconhecido'), 'error');
                }
                progressDiv.style.display = 'none';
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Falha no envio', 'error');
                progressDiv.style.display = 'none';
            });
        }

        function handleCreateFolder(e) {
            e.preventDefault();
            const formData = new FormData(e.target);

            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(`Pasta "${data.folder}" criada com sucesso`, 'success');
                    closeModal('createFolderModal');
                    loadFiles();
                    document.getElementById('folderName').value = '';
                } else {
                    showNotification('Falha ao criar pasta: ' + (data.error || 'Erro desconhecido'), 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Falha ao criar pasta', 'error');
            });
        }

        function zipSelected() {
            if (selectedFiles.size === 0) {
                showNotification('Por favor, selecione arquivos para compactar', 'warning');
                return;
            }

            document.getElementById('zipFiles').value = JSON.stringify(Array.from(selectedFiles));
            document.getElementById('zipModal').style.display = 'block';
        }

        function handleZip(e) {
            e.preventDefault();
            const formData = new FormData(e.target);

            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                // Check if response is ok
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Check content type
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    return response.text().then(text => {
                        console.error('Non-JSON response:', text);
                        throw new Error('Server returned non-JSON response: ' + text.substring(0, 100));
                    });
                }

                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showNotification(data.message || `Arquivo "${data.zip}" criado com sucesso`, 'success');
                    closeModal('zipModal');
                    loadFiles();
                    selectedFiles.clear();
                    updateSelectionButtons();
                } else {
                    showNotification('Falha ao criar arquivo: ' + (data.error || 'Erro desconhecido'), 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Falha ao criar arquivo: ' + error.message, 'error');
            });
        }

        function unzipFile(filePath) {
            // Show extraction modal for better user experience
            showExtractionModal(filePath);
        }

        function showExtractionModal(filePath) {
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>📦 Extrair Arquivo</h3>
                        <button class="close" onclick="this.closest('.modal').remove()">&times;</button>
                    </div>
                    <form id="extractForm">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                        <input type="hidden" name="action" value="unzip">
                        <input type="hidden" name="zip_file" value="${filePath}">

                        <div class="form-group">
                            <label>Arquivo: ${filePath.split('/').pop()}</label>
                        </div>

                        <div class="form-group">
                            <label for="extractPath">Pasta de destino (opcional):</label>
                            <input type="text" id="extractPath" name="extract_to" placeholder="Deixe vazio para extrair na mesma pasta">
                        </div>

                        <div style="text-align: right;">
                            <button type="button" class="btn btn-secondary" onclick="this.closest('.modal').remove()">Cancelar</button>
                            <button type="submit" class="btn btn-primary">Extrair</button>
                        </div>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // Handle form submission
            modal.querySelector('#extractForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = new FormData(this);

                fetch('', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification(data.message || 'Arquivo extraído com sucesso', 'success');
                        loadFiles();
                        modal.remove();
                    } else {
                        showNotification(data.error || 'Falha na extração', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Falha na extração', 'error');
                });
            });
        }

        function deleteSelected() {
            if (selectedFiles.size === 0) {
                showNotification('Por favor, selecione arquivos para excluir', 'warning');
                return;
            }

            if (!confirm(`Excluir ${selectedFiles.size} item(s) selecionado(s)?`)) return;

            const formData = new FormData();
            formData.append('csrf_token', '<?php echo $_SESSION['csrf_token']; ?>');
            formData.append('action', 'delete');
            formData.append('files', JSON.stringify(Array.from(selectedFiles)));

            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(`${data.deleted.length} item(s) excluído(s)`, 'success');
                    loadFiles();
                    selectedFiles.clear();
                    updateSelectionButtons();
                } else {
                    showNotification('Falha na exclusão: ' + (data.errors ? data.errors.join(', ') : 'Erro desconhecido'), 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Falha na exclusão', 'error');
            });
        }

        function deleteFile(filePath) {
            if (!confirm('Excluir este item?')) return;

            const formData = new FormData();
            formData.append('csrf_token', '<?php echo $_SESSION['csrf_token']; ?>');
            formData.append('action', 'delete');
            formData.append('files', JSON.stringify([filePath]));

            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Item excluído com sucesso', 'success');
                    loadFiles();
                } else {
                    showNotification('Falha na exclusão: ' + (data.errors ? data.errors.join(', ') : 'Erro desconhecido'), 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Falha na exclusão', 'error');
            });
        }

        function downloadFile(filePath) {
            // Criar URL de download
            const downloadUrl = `?action=download&file=${encodeURIComponent(filePath)}`;

            // Usar window.location para forçar o download
            window.location.href = downloadUrl;
        }

        function downloadDirectory(path) {
            showNotification('Preparando download do diretório...', 'info');

            const formData = new FormData();
            formData.append('csrf_token', '<?php echo $_SESSION['csrf_token']; ?>');
            formData.append('action', 'download_dir');
            formData.append('dir_path', path);

            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                // Check if response is ok
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Check content type
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    return response.text().then(text => {
                        console.error('Non-JSON response:', text);
                        throw new Error('Server returned non-JSON response');
                    });
                }

                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showNotification('Iniciando download...', 'success');
                    // Trigger download
                    window.location.href = data.download_url;
                } else {
                    showNotification(data.error || 'Erro ao preparar download', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Erro ao preparar download: ' + error.message, 'error');
            });
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.currentTarget.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');

            const files = Array.from(e.dataTransfer.files);
            document.getElementById('fileInput').files = e.dataTransfer.files;
            updateUploadArea(files);
        }

        function updateUploadArea(files) {
            const uploadArea = document.querySelector('.upload-area p');
            if (files.length > 0) {
                uploadArea.textContent = `${files.length} file(s) selected`;
            }
        }

        function setView(viewType) {
            // Toggle view buttons
            document.querySelectorAll('.view-toggle button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // This could be extended to implement grid view
            showNotification(`Alterado para visualização em ${viewType === 'list' ? 'lista' : 'grade'}`, 'info');
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => notification.classList.add('show'), 100);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        // Close modals when clicking outside
        window.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'a':
                        e.preventDefault();
                        document.getElementById('selectAll').checked = true;
                        toggleSelectAll();
                        break;
                    case 'u':
                        e.preventDefault();
                        showUploadModal();
                        break;
                    case 'n':
                        e.preventDefault();
                        showCreateFolderModal();
                        break;
                }
            }

            if (e.key === 'Delete' && selectedFiles.size > 0) {
                deleteSelected();
            }

            if (e.key === 'Escape') {
                document.querySelectorAll('.modal').forEach(modal => {
                    modal.style.display = 'none';
                });
            }
        });
    </script>
</body>
</html>
